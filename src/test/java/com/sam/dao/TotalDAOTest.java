package com.sam.dao;

import com.sam.dao.impl.TotalDAOImpl;
import com.sam.entity.Community;
import org.junit.Test;

import java.util.List;

/**
 * 测试数据库连接和小区数据获取
 */
public class TotalDAOTest {

    @Test
    public void testFindAllCommunity() {
        try {
            TotalDAOImpl dao = new TotalDAOImpl();
            List<Community> communities = dao.findAllCommunity();
            
            System.out.println("=== 小区数据测试结果 ===");
            System.out.println("获取到的小区数量: " + communities.size());
            
            if (communities.isEmpty()) {
                System.out.println("警告: 数据库中没有小区数据！");
                System.out.println("请检查:");
                System.out.println("1. 数据库连接是否正常");
                System.out.println("2. community表是否存在");
                System.out.println("3. community表中是否有数据");
            } else {
                System.out.println("小区列表:");
                for (Community community : communities) {
                    System.out.println("- ID: " + community.getCommunityId() + 
                                     ", 名称: " + community.getCommunityName() + 
                                     ", 地址: " + community.getCommunityAddress());
                }
            }
        } catch (Exception e) {
            System.err.println("数据库连接或查询失败:");
            e.printStackTrace();
        }
    }
}

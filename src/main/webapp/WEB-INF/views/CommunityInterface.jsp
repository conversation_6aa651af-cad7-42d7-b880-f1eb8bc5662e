<%--
  Created by IntelliJ IDEA.
  User: sam
  Date: 2025/8/14
  Time: 19:09
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" isELIgnored="false" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>

<html>
<head>
    <title>小区信息管理</title>

    <script src="${pageContext.request.contextPath}/static/js/jquery-3.6.3.js"></script>
    <style>
        body {
            margin: 0;
            min-height: 100vh;
            padding: 20px 0;
            background-image: url("${pageContext.request.contextPath}/static/img/indexBgp.jpg");
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            background-attachment: fixed;
            font-family: "Helvetica Neue", Arial, sans-serif;
            /* 垂直滚动 */
            overflow-y: auto;
        }

        .container {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;
            min-height: calc(100vh - 40px);
            gap: 30px;
            padding: 20px;
        }

        /* 毛玻璃表单 */
        .glass-form {
            width: 400px;
            max-width: 90vw;
            padding: 25px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            margin: 0 auto 30px auto;
            align-self: center;
        }

        .glass-form h2 {
            color: #fff;
            margin-bottom: 20px;
        }

        .glass-form input[type="text"],
        .glass-form input[type="file"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: none;
            border-radius: 10px;
            outline: none;
        }

        .glass-form button {
            width: 100%;
            padding: 12px;
            margin-top: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.4);
            color: #000;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .glass-form button:hover {
            background: rgba(255, 255, 255, 0.7);
        }

        .preview {
            margin-top: 10px;
        }

        .preview img {
            width: 100%;
            border-radius: 10px;
            display: none; /* 初始隐藏 */
        }

        hr {
            width: 80%;
            border: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.5);
            margin: 20px auto;
        }

        /* MacOS 风格小区列表 */
        .community-list {
            width: 80%;
            max-width: 800px;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            margin: 0 auto;
            align-self: center;
        }

        /* 模拟 MacOS 窗口的标题栏 */
        .community-list .title-bar {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: rgba(240, 240, 240, 0.8);
            border-bottom: 1px solid rgba(200, 200, 200, 0.6);
        }

        .title-bar .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 6px;
        }
        .dot.red { background: #ff5f56; }
        .dot.yellow { background: #ffbd2e; }
        .dot.green { background: #27c93f; }

        /* 小区信息表格 */
        table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }
        th, td {
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.5);
        }
        th {
            background: rgba(255, 255, 255, 0.4);
        }

        input[type="file"] {
            display: none;
        }

        .custom-btn {
            width: 94%;
            padding: 12px;
            margin-top: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.4);
            color: #000;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
            text-align: center;
            display: inline-block;
        }

        .custom-btn:hover {
            background: rgba(255, 255, 255, 0.7); /* hover 效果和表单按钮一样 */
        }


    </style>
</head>
<body>
<div class="container">
    <%--表单--%>
    <div class="glass-form">
        <h2>添加小区</h2>
        <form id="communityForm"
              enctype="multipart/form-data"
              action="${pageContext.request.contextPath}/TotalServlet?to=insertCommunity"
              method="post">
            <input type="text" id="communityName" name="communityName" placeholder="输入小区名称" required>
            <input type="text" id="communityAddress" name="communityAddress" placeholder="输入小区地址" required>

            <div class="file-upload">
                <label for="communityPicture" class="custom-btn">选择图片</label>
                <input type="file" id="communityPicture" name="communityPicture" accept="image/*">
            </div>
            <div class="preview">
                <img id="previewImg" alt="图片预览">
            </div>
            <button type="submit">添加小区</button>
        </form>
    </div>
</div>

    <!-- 提示信息显示区域 -->
    <c:if test="${not empty tip}">
        <div style="text-align: center; margin: 20px 0;">
            <div style="display: inline-block; padding: 10px 20px; background: rgba(76, 175, 80, 0.8); color: white; border-radius: 10px; backdrop-filter: blur(10px);">
                ${tip}
            </div>
        </div>
    </c:if>

    <c:if test="${not empty insertErr}">
        <div style="text-align: center; margin: 20px 0;">
            <div style="display: inline-block; padding: 10px 20px; background: rgba(244, 67, 54, 0.8); color: white; border-radius: 10px; backdrop-filter: blur(10px);">
                ${insertErr}
            </div>
        </div>
    </c:if>

    <c:if test="${not empty error}">
        <div style="text-align: center; margin: 20px 0;">
            <div style="display: inline-block; padding: 10px 20px; background: rgba(255, 152, 0, 0.8); color: white; border-radius: 10px; backdrop-filter: blur(10px);">
                ${error}
            </div>
        </div>
    </c:if>

    <hr>

<table id="communityTable" class="community-list">
    <thead>
    <tr>
        <th>小区名称</th>
        <th>小区地址</th>
        <th>照片</th>
        <th>操作</th>
    </tr>
    </thead>
    <tbody>
    <c:forEach var="community" items="${communityList}">
        <tr>
            <td>${community.communityName}</td>
            <td>${community.communityAddress}</td>
            <td>
                <c:choose>
                    <c:when test="${not empty community.communityPicture}">
                        有照片
                    </c:when>
                    <c:otherwise>
                        无照片
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <a href="PageServlet?to=BuildingInterface_jsp">查看详情</a>
            </td>
        </tr>
    </c:forEach>
    </tbody>
</table>


        <script src="${pageContext.request.contextPath}/static/js/jquery-3.6.3.js"></script>
        <script src="${pageContext.request.contextPath}/static/js/jquery.validate.js"></script>
<script>
    $(function () {
        $("#communityForm").validate({
            rules: {
                communityAddress: {
                    required: true,
                    remote: {
                        url: "${pageContext.request.contextPath}/TotalServlet?to=checkAddress",
                        type: "post",
                        dataType: "json", // 自动解析
                        data: {
                            communityAddress: function () {
                                return $("#communityAddress").val();
                            }
                        },
                        dataFilter: function (data) {
                            // data 已经是对象了
                            return data.code !== 500;
                        }
                    }
                }
            },
            messages: {
                communityAddress: {
                    required: "请输入地址",
                    remote: "该地址已存在，请换一个"
                }
            }
        });
    });
</script>
</body>
</html>
<%--
  Created by IntelliJ IDEA.
  User: sam
  Date: 2025/8/16
  Time: 21:25
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" isELIgnored="false" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>

<html>
<head>
    <title>楼栋信息管理</title>
    <script src="${pageContext.request.contextPath}/static/js/jquery-3.6.3.js"></script>
    <style>
        body {
            margin: 0;
            min-height: 100vh;
            padding: 20px 0;
            background-image: url("${pageContext.request.contextPath}/static/img/indexBgp.jpg");
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            background-attachment: fixed;
            font-family: "Helvetica Neue", Arial, sans-serif;
            overflow-y: auto;
        }

        .container {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;
            min-height: calc(100vh - 40px);
            gap: 30px;
            padding: 20px;
        }

        /* ================== 上方表单 ================== */
        .glass-form {
            width: 400px;
            max-width: 90vw;
            padding: 25px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            margin: 0 auto 30px auto;
            align-self: center;
        }

        .glass-form h2 {
            color: #fff;
            margin-bottom: 20px;
        }

        .glass-form input[type="text"],
        .glass-form input[type="number"],
        .glass-form input[type="file"],
        .glass-form input[readonly] {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: none;
            border-radius: 10px;
            outline: none;
            background: rgba(255, 255, 255, 0.3);
            color: #000;
            font-size: 16px;
            box-sizing: border-box;
        }

        .glass-form input[readonly] {
            cursor: not-allowed;
        }

        .glass-form input[type=number]::-webkit-inner-spin-button,
        .glass-form input[type=number]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .glass-form button {
            width: 100%;
            padding: 12px;
            margin-top: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.4);
            color: #000;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .glass-form button:hover {
            background: rgba(255, 255, 255, 0.7);
        }

        input[type="file"] {
            display: none;
        }

        .custom-btn {
            width: 94%;
            padding: 12px;
            margin-top: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.4);
            color: #000;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
            text-align: center;
            display: inline-block;
        }

        .custom-btn:hover {
            background: rgba(255, 255, 255, 0.7);
        }

        /* ================== 下方楼栋表格（MacOS 风格） ================== */
        hr {
            width: 80%;
            border: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.5);
            margin: 20px auto;
        }

        .community-list {
            width: 80%;
            max-width: 800px;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            margin: 0 auto;
            align-self: center;
        }

        .community-list .title-bar {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: rgba(240, 240, 240, 0.8);
            border-bottom: 1px solid rgba(200, 200, 200, 0.6);
        }

        .title-bar .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 6px;
        }
        .dot.red { background: #ff5f56; }
        .dot.yellow { background: #ffbd2e; }
        .dot.green { background: #27c93f; }

        table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }
        th, td {
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.5);
        }
        th {
            background: rgba(255, 255, 255, 0.4);
        }

        /* 删除按钮样式 */
        .delete-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            background: rgba(244, 67, 54, 0.8);
            color: #fff;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .delete-btn:hover {
            background: rgba(255, 80, 80, 1);
        }
    </style>
</head>
<body>
<div class="container">
    <%--表单--%>
    <div class="glass-form">
        <h2>添加楼栋</h2>
        <form id="BuildingForm"
              action="${pageContext.request.contextPath}/TotalServlet?to=insertBuilding"
              method="post">

            <input type="text" id="communityId" name="communityId"
                   value="${communityId}"
                   placeholder="小区ID"
                   readonly
                   style="background-color: #f5f5f5; cursor: not-allowed;" required>

            <!-- 楼栋编号 -->
            <input type="text" id="buildingId" name="buildingId"
                   placeholder="请输入楼栋编号" required>

            <!-- 楼栋高度 -->
            <input type="number" id="height" name="height"
                   placeholder="输入楼栋高度(层数)"
                   min="1" max="100" required>

            <button type="submit">添加楼栋</button>
        </form>
    </div>
</div>

<!-- 提示信息显示区域 -->
<c:if test="${not empty tip}">
    <div style="text-align: center; margin: 20px 0;">
        <div style="display: inline-block; padding: 10px 20px; background: rgba(76, 175, 80, 0.8); color: white; border-radius: 10px; backdrop-filter: blur(10px);">
                ${tip}
        </div>
    </div>
</c:if>

<c:if test="${not empty insertErr}">
    <div style="text-align: center; margin: 20px 0;">
        <div style="display: inline-block; padding: 10px 20px; background: rgba(244, 67, 54, 0.8); color: white; border-radius: 10px; backdrop-filter: blur(10px);">
                ${insertErr}
        </div>
    </div>
</c:if>

<c:if test="${not empty error}">
    <div style="text-align: center; margin: 20px 0;">
        <div style="display: inline-block; padding: 10px 20px; background: rgba(255, 152, 0, 0.8); color: white; border-radius: 10px; backdrop-filter: blur(10px);">
                ${error}
        </div>
    </div>
</c:if>

<hr>

<table id="buildingTable" class="community-list">
    <thead>
    <tr>
        <th>楼栋编号</th>
        <th>小区ID</th>
        <th>楼栋高度(层)</th>
        <th>操作</th>
    </tr>
    </thead>
    <tbody>
    <c:choose>
        <c:when test="${not empty buildingList}">
            <c:forEach var="building" items="${buildingList}">
                <tr>
                    <td>${building.buildingId}</td>
                    <td>${building.communityId}</td>
                    <td>${building.height}</td>
                    <td>
                        <button onclick="deleteBuilding('${building.buildingId}')"
                                style="background: rgba(244, 67, 54, 0.8); color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer;">
                            删除
                        </button>
                    </td>
                </tr>
            </c:forEach>
        </c:when>
        <c:otherwise>
            <tr>
                <td colspan="4" style="text-align: center; color: #666;">
                    暂无楼栋数据
                </td>
            </tr>
        </c:otherwise>
    </c:choose>
    </tbody>
</table>


<script src="${pageContext.request.contextPath}/static/js/jquery-3.6.3.js"></script>
<script>
    // 删除楼栋功能
    function deleteBuilding(buildingId) {
        if (confirm('确定要删除楼栋 "' + buildingId + '" 吗？')) {
            $.ajax({
                url: '${pageContext.request.contextPath}/TotalServlet?to=deleteBuilding',
                type: 'POST',
                data: { buildingId: buildingId },
                dataType: 'json',
                success: function(result) {
                    if (result.code === 200) {
                        alert('删除成功！');
                        // 刷新页面以显示最新数据
                        window.location.reload();
                    } else {
                        alert('删除失败：' + result.message);
                    }
                },
                error: function() {
                    alert('删除失败：网络错误');
                }
            });
        }
    }

    // 表单验证
    $(function () {
        $("#BuildingForm").submit(function(e) {
            var buildingId = $("#buildingId").val().trim();
            var height = $("#height").val();
            var communityId = $("#communityId").val();

            if (!buildingId) {
                alert("请输入楼栋编号");
                return false;
            }

            if (!height || height < 1 || height > 100) {
                alert("请输入有效的楼栋高度(1-100层)");
                return false;
            }

            if (!communityId) {
                alert("小区ID不能为空");
                return false;
            }

            return true;
        });
    });
</script>
</body>
</html>

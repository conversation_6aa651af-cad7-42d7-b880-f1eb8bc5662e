<%--
  Created by IntelliJ IDEA.
  User: sam
  Date: 2025/8/16
  Time: 21:25
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" isELIgnored="false" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<html>
<head>
    <title>小区信息界面</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="${pageContext.request.contextPath}/static/layui/css/layui.css" rel="stylesheet">
    <script src="${pageContext.request.contextPath}/static/layui/layui.js"></script>
</head>
<body>
<style>
    .demo-container {
        width: 360px;
        margin: 30px auto;
    }
    .layui-upload-img {
        max-width: 100%;
        margin-top: 10px;
        border-radius: 8px;
        border: 1px solid #eee;
        display: block;
    }
</style>

<form
      class="layui-form demo-container"
      lay-filter="communityForm"
      action="${pageContext.request.contextPath}/TotalServlet?to=insertCommunity"
      method="post">


    <!-- 小区名称 -->
    <div class="layui-form-item">
        <div class="layui-input-wrap">
            <div class="layui-input-prefix">
                <i class="layui-icon layui-icon-home"></i>
            </div>
            <input type="text" name="communityName" lay-verify="required"
                   placeholder="请输入小区名称" lay-reqtext="小区名称不能为空"
                   autocomplete="off" class="layui-input" lay-affix="clear">
        </div>
    </div>

    <!-- 小区地址 -->
    <div class="layui-form-item">
        <div class="layui-input-wrap">
            <div class="layui-input-prefix">
                <i class="layui-icon layui-icon-location"></i>
            </div>
            <input type="text" name="communityAddress" lay-verify="required"
                   placeholder="请输入小区详细地址" lay-reqtext="地址不能为空"
                   autocomplete="off" class="layui-input" lay-affix="clear">
        </div>
    </div>

    <!-- 图片上传 -->
    <div class="layui-form-item">
        <button type="button" class="layui-btn layui-btn-fluid" id="uploadImage">
            <i class="layui-icon">&#xe67c;</i> 上传小区图片
        </button>
        <div class="layui-upload-list">
            <img class="layui-upload-img" id="previewImg">
        </div>
        <!-- 隐藏字段，用于保存上传后的图片路径 -->
        <input type="hidden" name="communityPicture" id="communityPicture">
    </div>

    <!-- 提交按钮 -->
    <div class="layui-form-item">
        <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="submitCommunity">
            录入
        </button>
    </div>
</form>

<%---------------------功能--------------------------%>

<script>
    layui.use(['upload', 'form'], function () {
        var upload = layui.upload;
        var form = layui.form;

        // 图片上传
        upload.render({
            elem: '#uploadImage',
            url: '${pageContext.request.contextPath}/TotalServlet?to=uploadImage', // 你后端处理上传的接口
            accept: 'images',
            done: function (res) {
                if (res.code === 0) {  // 假设返回格式 {code:0, msg:"ok", data:{src:"xxx.jpg"}}
                    layer.msg('上传成功');
                    // 设置预览图
                    document.getElementById("previewImg").src = res.data.src;
                    // 把图片路径保存到隐藏字段
                    document.getElementById("communityPicture").value = res.data.src;
                } else {
                    layer.msg('上传失败：' + res.msg);
                }
            },
            error: function () {
                layer.msg('请求出错');
            }
        });

        // 2. 表单提交直接让 form 走 action
        form.on('submit(submitCommunity)', function (data) {
            // data.field 里有表单数据
            console.log("表单提交数据：", data.field);
            // return true 让表单正常提交
            return true;
        });

    });
</script>

</body>
</html>
package com.sam.service;

import com.sam.entity.Building;
import com.sam.entity.Community;
import com.sam.util.ResultVO;

import java.util.List;

public interface ITotalService {

    //查询所有小区信息
    ResultVO<List<Community>> findAllCommunity();

    //查询所有楼栋信息
    ResultVO<List<Building>> findBuildingByCommunityId(Integer id);

    //添加小区
    ResultVO<?> insertCommunity(Community community);

    //添加楼栋
    ResultVO<?> insertBuilding(Building building);

    //删除楼栋
    ResultVO<?> deleteBuilding(String buildingId);

    //表单验证，验证是否有重名小区
    ResultVO checkAddress(String communityAddress);
}

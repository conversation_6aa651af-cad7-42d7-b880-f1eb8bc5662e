package com.sam.service.impl;

import com.sam.dao.ITotalDAO;
import com.sam.dao.impl.TotalDAOImpl;
import com.sam.entity.Building;
import com.sam.entity.Community;
import com.sam.service.ITotalService;
import com.sam.util.ResultVO;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-16 22:03
 */
public class TotalServiceImpl implements ITotalService {

    //创建DAO层接口对象
    private ITotalDAO totalDAO = new TotalDAOImpl();

    /**
     *查询所有小区
     */
    @Override
    public ResultVO<List<Community>> findAllCommunity() {
        ResultVO<List<Community>> resultVO = new ResultVO<>();
        try {
            List<Community> list = totalDAO.findAllCommunity(); // 调用 DAO
            resultVO.setCode(200);
            resultVO.setMessage("查询成功");
            resultVO.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            resultVO.setCode(500);
            resultVO.setMessage("查询失败：" + e.getMessage());
            resultVO.setData(null);
        }
        return resultVO;
    }

    /**
     *查询所有楼栋
     */
    @Override
    public ResultVO<List<Building>> findBuildingByCommunityId(Integer id) {
        List<Building> buildingList = totalDAO.findBuildingByCommunityId(id);
        return ResultVO.success(buildingList);
    }

    /**
     * 添加小区信息
     */
    @Override
    public ResultVO<?> insertCommunity(Community community) {
        if(Objects.isNull(community)){
            return ResultVO.error("添加失败");
        }
        //调用DAO层
        int n = totalDAO.insertCommunity(community);
        if(n>0){
            return ResultVO.success("添加成功");
        }
        return ResultVO.error("添加失败");
    }

    /**
     *插入楼栋信息
     */
    @Override
    public ResultVO<?> insertBuilding(Building building) {
        if(Objects.isNull(building)){
            return ResultVO.error("添加失败");
        }
        //调用DAO层方法
        int n = totalDAO.insertBuilding(building);
        if(n>0){
            return ResultVO.success("添加成功");
        }
        return ResultVO.error("添加失败");
    }

    /**
     *删除楼栋
     */

    @Override
    public ResultVO<?> deleteBuilding(String buildingId) {
        if(Objects.isNull(buildingId) || buildingId.trim().isEmpty()){
            return ResultVO.error("楼栋ID不能为空");
        }
        int n = totalDAO.deleteBuilding(buildingId);
        if(n>0){
            return ResultVO.success("删除成功");
        }
        return ResultVO.error("删除失败");
    }

    /**
     * 表单验证
     */
    @Override
    public ResultVO checkAddress(String communityAddress) {
        long count = totalDAO.checkAddress(communityAddress);
        if(count>0){
            return ResultVO.error("该小区已经录入，请在列表中查询!");
        }
        return ResultVO.success("已经成功录入!");
    }
}

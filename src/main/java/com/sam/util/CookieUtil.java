package com.sam.util;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-08 17:40
 */
public class CookieUtil {
    /**
     * 创建Cookie
     * @param name  Cookie名称
     * @param value Cookie值
     * @param maxAge 有效期（秒），-1表示浏览器关闭失效
     * @param domain 作用域
     * @param path   作用路径
     * @return 生成的Cookie对象
     */
    public static Cookie createCookie(String name, String value, int maxAge, String domain, String path) {
        Cookie cookie = new Cookie(name, value);
        cookie.setMaxAge(maxAge);
        if (domain != null) cookie.setDomain(domain);
        cookie.setPath(path != null ? path : "/");
        return cookie;
    }

    /**
     * 获取Cookie值
     * @param request HttpServletRequest对象
     * @param name    Cookie名称
     * @return Cookie值，找不到返回null
     */
    public static String getCookieValue(HttpServletRequest request, String name) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(name)) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 修改Cookie值
     * @param request  HttpServletRequest对象
     * @param response HttpServletResponse对象
     * @param name     Cookie名称
     * @param newValue 新的Cookie值
     */
    public static void updateCookie(HttpServletRequest request, HttpServletResponse response,
                                    String name, String newValue) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(name)) {
                    cookie.setValue(newValue);
                    // 写入到客户端浏览器
                    response.addCookie(cookie);
                    break;
                }
            }
        }
    }

    /**
     * 删除Cookie
     * @param request  HttpServletRequest对象
     * @param response HttpServletResponse对象
     * @param name     Cookie名称
     */
    public static void deleteCookie(HttpServletRequest request, HttpServletResponse response, String name) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(name)) {
                    cookie.setMaxAge(0);
                    cookie.setPath("/");
                    cookie.setValue(null);
                    response.addCookie(cookie);
                    break;
                }
            }
        }
    }

    /**
     * 设置安全Cookie（HTTPS-only）
     * @param cookie 要设置的Cookie对象
     * @return 配置后的Cookie对象
     */
    public static Cookie makeSecure(Cookie cookie) {
        cookie.setSecure(true);
        return cookie;
    }

    /**
     * 设置HttpOnly属性
     * @param cookie 要设置的Cookie对象
     * @return 配置后的Cookie对象
     */
    public static Cookie makeHttpOnly(Cookie cookie) {
        cookie.setHttpOnly(true);
        return cookie;
    }
}

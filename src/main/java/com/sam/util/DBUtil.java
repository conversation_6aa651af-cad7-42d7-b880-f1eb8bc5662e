package com.sam.util;

import com.alibaba.druid.pool.DruidDataSourceFactory;

import javax.sql.DataSource;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Properties;

public class DBUtil {
    static Properties properties;

    static {
        try {
            properties = new Properties();
            InputStream is = DBUtil.class.getClassLoader().
                    getResourceAsStream("druid.properties");
            properties.load(is);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    // 单例数据源
    private static volatile DataSource dataSource;
    // 使用ThreadLocal隔离Connection，支持事务
    private static final ThreadLocal<Connection> connectionHolder = new ThreadLocal<>();

    // 初始化数据源（双重检查锁）
    private static DataSource getDataSource() {
        try {
            if (dataSource == null) {
                synchronized (DBUtil.class) {
                    if (dataSource == null) {
                        //加载属性文件创建数据库连接池
                        dataSource = DruidDataSourceFactory.createDataSource(properties);
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return dataSource;
    }

    // 获取连接（自动管理事务）
    public static Connection getConnection() {
        Connection conn = null;
        try {
            conn = connectionHolder.get();
            if (conn == null || conn.isClosed()) {
                conn = getDataSource().getConnection();
                connectionHolder.set(conn);
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return conn;
    }

    // 开启事务
    public static void beginTransaction() {
        try {
            Connection conn = getConnection();
            // 手动提交事务
            conn.setAutoCommit(false);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    // 提交事务并释放连接
    public static void commit() {
        Connection conn = connectionHolder.get();
        if (conn != null) {
            try {
                conn.commit();
                System.out.println("事务提交成功!");
            } catch (SQLException e) {
                throw new RuntimeException("事务提交失败!", e);
            } finally {
                releaseConnection(conn);
            }
        }
    }

    // 回滚事务
    public static void rollback() {
        Connection conn = connectionHolder.get();
        if (conn != null) {
            try {
                conn.rollback();
                System.out.println("事务回滚成功!");
            } catch (SQLException e) {
                throw new RuntimeException("事务回滚失败!", e);
            } finally {
                releaseConnection(conn);
            }
        }
    }

    // 释放连接（从ThreadLocal移除并关闭）
    private static void releaseConnection(Connection conn) {
        try {
            if (conn != null && !conn.isClosed()) {
                conn.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            connectionHolder.remove();
        }
    }


    // 执行查询（自动释放资源）
    public static ResultSet executeQuery(String sql, Object... params) {
        ResultSet rs = null;
        try {
            Connection conn = getConnection();
            PreparedStatement ps = conn.prepareStatement(sql);
            setParameters(ps, params);
            rs = ps.executeQuery();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return rs;
    }

    // 执行更新（带事务支持）
    public static int executeUpdate(String sql, Object... params) {
        int n = 0;
        Connection conn = getConnection();
        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            setParameters(ps, params);
            n = ps.executeUpdate();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return n;
    }

    // 参数绑定（防止SQL注入）
    private static void setParameters(PreparedStatement ps, Object... params)  {
        try {
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }
}

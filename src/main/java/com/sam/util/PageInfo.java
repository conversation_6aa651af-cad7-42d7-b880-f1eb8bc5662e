package com.sam.util;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-14 10:38
 */
public class PageInfo<T> {
    /**
     * 当前页码
     */
    private int pageNum;
    /** 每页显示的记录数*/
    private int pageSize;
    /**
     * 每页查到的记录存放的集合,通过查询数据库得到
     */
    private List<T> data;
    /**总页数，通过计算得到*/
    private int totalPage;
    /**总记录数，通过查询数据库得到*/
    private long totalRecord;
    public PageInfo() {
    }
    public PageInfo(int pageNum, int pageSize) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }
    public PageInfo(int pageNum, int pageSize, List<T> data, int totalPage, long
            totalRecord) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.data = data;
        this.totalPage = totalPage;
        this.totalRecord = totalRecord;
    }
    public int getPageNum() {
        return pageNum;
    }
    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }
    public int getPageSize() {
        return pageSize;
    }
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
    public List<T> getData() {
        return data;
    }
    public void setData(List<T> data) {
        this.data = data;
    }
    public int getTotalPage() {
        return totalPage;
    }
    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }
    public long getTotalRecord() {
        return totalRecord;
    }
    public void setTotalRecord(long totalRecord) {
        this.totalRecord = totalRecord;
    }
    @Override
    public String toString() {
        return "PageInfo{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", totalPage=" + totalPage +
                ", totalRecord=" + totalRecord +
                '}';
    }
}

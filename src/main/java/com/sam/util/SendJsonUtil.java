package com.sam.util;

import com.alibaba.fastjson2.JSON;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.PrintWriter;

/**
 * 发送JSON数据给前端
 */
public class SendJsonUtil {
    public static void sendJson(HttpServletResponse response,Object obj) {
        try {
            //向浏览器传递json格式的字符串
            response.setContentType("application/json;charset=UTF-8");
            // 使用alibaba提供的工具，将java对象转换成json格式的字符串
            String str = JSON.toJSONString(obj);
            System.out.println(str); //可以在控制台输出一下查看
            PrintWriter pw = response.getWriter(); //字符输出流
            pw.println(str); //写的方法
            pw.flush();
            pw.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}

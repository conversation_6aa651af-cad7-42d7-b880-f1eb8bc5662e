package com.sam.dao;

import com.sam.entity.Building;
import com.sam.entity.Community;

import java.util.List;

public interface ITotalDAO {

    //查询所有小区信息
    List<Community> findAllCommunity();

    //查询所有楼栋信息
    List<Building> findBuildingByCommunityId(Integer communityId);

    //添加小区
    int insertCommunity(Community community);

    //添加楼栋
    int insertBuilding(Building building);

    //删除楼栋
    int deleteBuilding(String buildingId);

    //检查小区地址合法性
    long checkAddress(String communityAddress);

}

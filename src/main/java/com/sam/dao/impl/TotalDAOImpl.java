package com.sam.dao.impl;

import com.sam.dao.ITotalDAO;
import com.sam.entity.Building;
import com.sam.entity.Community;
import com.sam.util.DBUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-16 22:04
 */
public class TotalDAOImpl implements ITotalDAO {

    /**
     *查询所有小区的信息，用于列出小区表格
     */
    @Override
    public List<Community> findAllCommunity() {
        //创建数组，用于存储值
        List<Community> communityList = new ArrayList<Community>();
        //查询语句
        String sql = "select communityId,communityName,communityPicture,communityAddress from community";
        //执行SQL语句，返回结果集
        ResultSet rs = DBUtil.executeQuery(sql);

        try {
            //移动到下一行，并判断是否还有数据
            while (rs.next()) {
                //new一个新的实体对象
                Community community = new Community();
                //遍历所有结果
                community.setCommunityId(rs.getInt("communityId"));
                community.setCommunityName(rs.getString("communityName"));
                community.setCommunityPicture(rs.getString("communityPicture"));
                community.setCommunityAddress(rs.getString("communityAddress"));
                //加入集合
                communityList.add(community);
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        //返回已经填充数据的数组
        return communityList;

    }

    /**
     *查询所有楼栋的信息，用于在小区界面点击详情后跳转到楼栋界面后列出表格
     */

    @Override
    public List<Building> findBuildingByCommunityId(Integer communityId) {
        // 创建集合，用于存储结果
        List<Building> buildingList = new ArrayList<>();
        // 查询语句
        String sql = "SELECT buildingId, communityId, height FROM building WHERE communityId = ?";
        ResultSet rs = DBUtil.executeQuery(sql, communityId);

        try {
            while (rs.next()) {  // 遍历所有结果
                Building building = new Building();
                building.setBuildingId(rs.getString("buildingId"));
                building.setCommunityId(rs.getInt("communityId"));
                building.setHeight(rs.getInt("height"));
                // 加入集合
                buildingList.add(building);
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        // 返回完整集合
        return buildingList;
    }


    /**
     *新增小区，往数据库里插入数据
     */

    @Override
    public int insertCommunity(Community community) {
        String sql = "INSERT INTO community(communityName, communityPicture, communityAddress) VALUES(?,?,?)";
        Object[] params = {
                community.getCommunityName(),
                community.getCommunityPicture(),
                community.getCommunityAddress()
        };
        return DBUtil.executeUpdate(sql, params);
    }


    /**
     *新增楼栋，往数据库里插入数据
     */

    @Override
    public int insertBuilding(Building building) {
        //SQL语句
        String sql = "INSERT INTO building(buildingId, communityId, height) VALUES(?, ?, ?)";
        Object[] params = {
                building.getBuildingId(),
                building.getCommunityId(),
                building.getHeight()
        };
        return DBUtil.executeUpdate(sql, params);
    }

    /**
     * 删除楼栋信息
     * */

    @Override
    public int deleteBuilding(String buildingId) {
        //SQL语句
        String sql = "DELETE FROM building WHERE buildingId = ?";
        try(
                Connection conn = DBUtil.getConnection();
                PreparedStatement ps = conn.prepareStatement(sql))
                 {
                     ps.setString(1, buildingId);
                     return ps.executeUpdate();
        }catch (SQLException e){
            e.printStackTrace();
            return 0;
        }
    }

    /**
     *检查地址合法性
     */
    @Override
    public long checkAddress(String communityAddress) {
        String sql = "select count(*) from community where communityAddress = ?";
        ResultSet rs = DBUtil.executeQuery(sql,communityAddress);

        try {
            if(rs.next()){
                int n = rs.getInt(1);
                return n;
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return 0;
    }
}

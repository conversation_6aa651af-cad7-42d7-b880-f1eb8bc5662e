package com.sam.controller;

import cn.hutool.core.util.ReflectUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Method;

/**
 * 模仿SpringMVC框架的Controller类，一个类可以处理多个请求
 * ManagerServlet继承 BaseServlet , BaseServlet 继承 HttpServlet
 */
public class BaseServlet extends HttpServlet {
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        //在doGet方法中调用doPost,这样doPost就可以集中处理所有请求
        this.doPost(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String ip = request.getRemoteAddr();
        //http://localhost:8080/vote_system/Servlet的value属性值?to=方法名
        //1.获取请求路径上的参数值 to
        request.setCharacterEncoding("utf-8");
        String methodName = request.getParameter("to");
        //2.利用反射的知识点获取调用Servlet名称
        // this 就是客户端正在访问的Servlet
        Class<? extends BaseServlet> clazz = this.getClass();
        String servletName = clazz.getName();
        System.out.println(ip+"正在访问" + servletName+"的" + methodName+"方法!");
        //3.帮客户端调用对应的方法
        // hutool工具包中提供反射机制的方法
        try {
            Method method = ReflectUtil.getMethod(clazz,
                    methodName, HttpServletRequest.class, HttpServletResponse.class
            );
            method.invoke(this,request,response);

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(ip+"你的请求方法写错误:请检查你的"+servletName+"类中是否有这个"+methodName+"方法!!!");
            //传递给前端浏览器的信息
            response.setCharacterEncoding("UTF-8");
            response.setContentType("text/html;charset=UTF-8");
            PrintWriter pw = response.getWriter();
            pw.println(ip+"你的请求方法写错误:请检查你的"+servletName+"类中是否有这个"+methodName+"方法!!!"
            +e.getClass()+e.getMessage());
            pw.flush();
            pw.close();
        }

    }
}


package com.sam.controller;

import com.sam.entity.Community;
import com.sam.service.ITotalService;
import com.sam.service.impl.TotalServiceImpl;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;

@WebServlet(name = "PageServlet", value = "/PageServlet")
public class PageServlet extends BaseServlet {

    private ITotalService totalService = new TotalServiceImpl();

    protected void CommunityInterface_jsp(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            // 获取所有小区数据
            List<Community> communityList = totalService.findAllCommunity().getData();
            request.setAttribute("communityList", communityList);
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "获取小区数据失败：" + e.getMessage());
        }
        request.getRequestDispatcher("WEB-INF/views/CommunityInterface.jsp").forward(request,response);
    }

    protected void BuildingInterface_jsp(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.getRequestDispatcher("WEB-INF/views/BuildingInterface.jsp").forward(request,response);
    }

}

package com.sam.controller;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

@WebServlet(name = "PageServlet", value = "/PageServlet")
public class PageServlet extends BaseServlet {

    protected void CommunityInterface_jsp(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.getRequestDispatcher("WEB-INF/views/CommunityInterface.jsp").forward(request,response);
    }

    protected void BuildingInterface_jsp(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.getRequestDispatcher("WEB-INF/views/BuildingInterface.jsp").forward(request,response);
    }

}

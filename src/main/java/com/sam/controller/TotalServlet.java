package com.sam.controller;

import com.alibaba.fastjson2.JSON;
import com.sam.entity.Building;
import com.sam.entity.Community;
import com.sam.service.ITotalService;
import com.sam.service.impl.TotalServiceImpl;
import com.sam.util.*;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.MultipartConfig;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.Part;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/8/17
 */
@WebServlet(name = "TotalServlet", value = "/TotalServlet")
@MultipartConfig(maxFileSize = 1024 * 1024 * 5) // 5MB
public class TotalServlet extends BaseServlet {

    private ITotalService totalService = new TotalServiceImpl();

    /**
     * 新增小区信息
     */
    protected void insertCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            // 获取参数
            String communityName = request.getParameter("communityName");
            String communityAddress = request.getParameter("communityAddress");
            String communityPicture = ""; // 存入数据库的图片路径

            // 获取附件
            Part part = request.getPart("communityPicture");
            if (Objects.nonNull(part) && part.getSize() > 0) {
                try {
                    // 本地上传
                    String fileName = System.currentTimeMillis() + "_" + part.getSubmittedFileName();
                    String uploadDir = "/Users/<USER>/Downloads/Javaproject/DevImg/cswe"; // 本地目录
                    File file = new File(uploadDir, fileName);
                    part.write(file.getAbsolutePath());
                    // 存数据库的虚拟路径
                    communityPicture = "/uploads/" + fileName;
                } catch (Exception e) {
                    e.printStackTrace();
                    communityPicture = "";
                }
            }

            // 封装实体
            Community community = new Community();
            community.setCommunityName(communityName);
            community.setCommunityAddress(communityAddress);
            community.setCommunityPicture(communityPicture);

            // 调用 Service 层
            ResultVO resultVO = totalService.insertCommunity(community);

            if (resultVO.getCode() == 200) {
                request.setAttribute("tip", "添加成功！");
            } else {
                CookieUtil.deleteCookie(request, response, "Community");
                request.setAttribute("insertErr", resultVO.getMessage());
            }

            // 重新获取最新的小区列表数据
            try {
                List<Community> communityList = totalService.findAllCommunity().getData();
                request.setAttribute("communityList", communityList);
            } catch (Exception e) {
                e.printStackTrace();
                request.setAttribute("error", "获取小区列表失败：" + e.getMessage());
            }

            request.getRequestDispatcher("WEB-INF/views/CommunityInterface.jsp").forward(request, response);

        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("insertErr", "添加失败：" + e.getMessage());

            // 即使添加失败，也要获取现有的小区列表数据
            try{
                List<Community> communityList = totalService.findAllCommunity().getData();
                request.setAttribute("communityList", communityList);
            } catch (Exception ex) {
                ex.printStackTrace();
                request.setAttribute("error", "获取小区列表失败：" + ex.getMessage());
            }

            request.getRequestDispatcher("WEB-INF/views/CommunityInterface.jsp").forward(request, response);
        }
    }

    /**
     *检查小区地址是否重复
     */
    protected void checkAddress(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String communityAddress =request.getParameter("communityAddress");
        ResultVO resultVO = totalService.checkAddress(communityAddress);
        response.setContentType("application/json;charset=UTF-8");
        String str = JSON.toJSONString(resultVO);
        PrintWriter pw = response.getWriter(); //字符输出流
        pw.println(str); //写的方法
        pw.flush();
        pw.close();
    }

    /**
     * 列出所有小区信息
     */
    protected void findAllCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            List<Community> communityList = totalService.findAllCommunity().getData();
            request.setAttribute("communityList", communityList);
            request.getRequestDispatcher("WEB-INF/views/CommunityInterface.jsp").forward(request, response);
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "查询失败：" + e.getMessage());
            request.getRequestDispatcher("WEB-INF/views/CommunityInterface.jsp").forward(request, response);
        }
    }

    /**
     * 列出所有楼栋信息
     */
    protected void findBuildingByCommunityId(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            // 获取楼栋对应的小区的id
            String communityIdStr = request.getParameter("communityId");
            if (communityIdStr == null || communityIdStr.trim().isEmpty()) {
                request.setAttribute("error", "检查一下数据库");
                request.getRequestDispatcher("WEB-INF/views/BuildingInterface.jsp").forward(request, response);
                return;
            }

            Integer communityId = Integer.parseInt(communityIdStr);
            // 获取所有楼栋数据
            List<Building> buildingList = totalService.findBuildingByCommunityId(communityId).getData();
            request.setAttribute("buildingList", buildingList);
            request.setAttribute("communityId", communityId);
            request.getRequestDispatcher("WEB-INF/views/BuildingInterface.jsp").forward(request, response);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            request.setAttribute("error", "小区ID格式错误：" + e.getMessage());
            request.getRequestDispatcher("WEB-INF/views/BuildingInterface.jsp").forward(request, response);
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "查询失败：" + e.getMessage());
            request.getRequestDispatcher("WEB-INF/views/BuildingInterface.jsp").forward(request, response);
        }
    }

    /**
     * 新增楼栋信息
     */
    protected void insertBuilding(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json;charset=UTF-8");

        try {
            String buildingId = request.getParameter("buildingId");
            String heightStr = request.getParameter("height");
            String communityIdStr = request.getParameter("communityId");

            // 参数验证
            if (buildingId == null || buildingId.trim().isEmpty()) {
                response.getWriter().write(JSON.toJSONString(ResultVO.error("楼栋编号不能为空")));
                return;
            }

            if (heightStr == null || heightStr.trim().isEmpty()) {
                response.getWriter().write(JSON.toJSONString(ResultVO.error("楼栋高度不能为空")));
                return;
            }

            if (communityIdStr == null || communityIdStr.trim().isEmpty()) {
                response.getWriter().write(JSON.toJSONString(ResultVO.error("小区ID不能为空")));
                return;
            }

            Integer height = Integer.parseInt(heightStr);
            Integer communityId = Integer.parseInt(communityIdStr);

            // 业务验证
            if (height < 1 || height > 100) {
                response.getWriter().write(JSON.toJSONString(ResultVO.error("楼栋高度必须在1-100层之间")));
                return;
            }

            //封装实体
            Building building = new Building();
            building.setBuildingId(buildingId.trim());
            building.setHeight(height);
            building.setCommunityId(communityId);

            //调用Service层
            ResultVO resultVO = totalService.insertBuilding(building);
            response.getWriter().write(JSON.toJSONString(resultVO));

        } catch (NumberFormatException e) {
            e.printStackTrace();
            response.getWriter().write(JSON.toJSONString(ResultVO.error("参数格式错误：" + e.getMessage())));
        } catch (Exception e) {
            e.printStackTrace();
            response.getWriter().write(JSON.toJSONString(ResultVO.error("添加失败：" + e.getMessage())));
        }
    }

    /**
     * 获取楼栋列表
     */
    protected void getBuildingList(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json;charset=UTF-8");

        try {
            String communityIdStr = request.getParameter("communityId");
            if (communityIdStr == null || communityIdStr.trim().isEmpty()) {
                response.getWriter().write(JSON.toJSONString(ResultVO.error("小区ID不能为空")));
                return;
            }

            Integer communityId = Integer.parseInt(communityIdStr);
            ResultVO<List<Building>> resultVO = totalService.findBuildingByCommunityId(communityId);
            response.getWriter().write(JSON.toJSONString(resultVO));

        } catch (NumberFormatException e) {
            e.printStackTrace();
            response.getWriter().write(JSON.toJSONString(ResultVO.error("小区ID格式错误")));
        } catch (Exception e) {
            e.printStackTrace();
            response.getWriter().write(JSON.toJSONString(ResultVO.error("获取楼栋列表失败：" + e.getMessage())));
        }
    }

    /**
     * 删除楼栋信息
     */
    protected void deleteBuilding(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json;charset=UTF-8");

        String buildingId = request.getParameter("buildingId");

        try {
            // 参数验证
            if (buildingId == null || buildingId.trim().isEmpty()) {
                response.getWriter().write(JSON.toJSONString(ResultVO.error("楼栋ID不能为空")));
                return;
            }

            ResultVO resultVO = totalService.deleteBuilding(buildingId);
            response.getWriter().write(JSON.toJSONString(resultVO));
        } catch (Exception e) {
            e.printStackTrace();
            response.getWriter().write(JSON.toJSONString(ResultVO.error("删除失败：" + e.getMessage())));
        }
    }
}